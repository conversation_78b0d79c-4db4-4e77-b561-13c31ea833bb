'use server';

import { createClient } from '@/services/supabase/server';
import { GroupClassInfo, ClassStats, ClassesResponse, ClassFilters, ClassItem } from '../types';

export async function getGroupInfo(groupId: string) {
  try {
    const supabase = await createClient();
    
    const { data: groupData, error: groupError } = await supabase
      .from('class_groups')
      .select(`
        id,
        name,
        description,
        category,
        is_active,
        max_capacity,
        instructor_id,
        branch_id,
        instructor:users (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        branch:branches (
          id,
          name
        )
      `)
      .eq('id', groupId)
      .is('deleted_at', null)
      .single();

    if (groupError) {
      console.error('Erro ao buscar informações da turma:', groupError);
      return { success: false, errors: 'Erro ao carregar informações da turma' };
    }

    if (!groupData) {
      return { success: false, errors: 'Turma não encontrada' };
    }

    if (!groupData.instructor) {
      console.error(`Inconsistência de dados: Instrutor não encontrado para a turma ${groupId}`);
      return { success: false, errors: 'O instrutor associado a esta turma não foi encontrado.' };
    }

    if (!groupData.branch) {
      console.error(`Inconsistência de dados: Filial não encontrada para a turma ${groupId}`);
      return { success: false, errors: 'A filial associada a esta turma não foi encontrada.' };
    }

    // Ajustar para lidar com retorno como objeto ou array
    const instructorData = Array.isArray(groupData.instructor)
      ? groupData.instructor[0]
      : (groupData.instructor as any);

    const branchData = Array.isArray(groupData.branch)
      ? groupData.branch[0]
      : (groupData.branch as any);

    const info: GroupClassInfo = {
      id: groupData.id,
      name: groupData.name,
      description: groupData.description,
      category: groupData.category,
      isActive: groupData.is_active,
      maxCapacity: groupData.max_capacity,
      instructor: {
        id: instructorData.id,
        name: `${instructorData.first_name} ${instructorData.last_name}`.trim(),
        avatar: instructorData.avatar_url,
      },
      branch: {
        id: branchData.id,
        name: branchData.name,
      },
    };

    return { success: true, data: info };
  } catch (error) {
    console.error('Erro inesperado ao buscar informações da turma:', error);
    return { success: false, errors: 'Erro interno do servidor' };
  }
}

export async function getGroupClassStats(groupId: string) {
  try {
    const supabase = await createClient();
    
    const { data: statsData, error: statsError } = await supabase
      .from('classes')
      .select('status')
      .eq('class_group_id', groupId)
      .is('deleted_at', null);

    if (statsError) {
      console.error('Erro ao buscar estatísticas:', statsError);
      return { success: false, errors: 'Erro ao carregar estatísticas' };
    }

    const stats: ClassStats = {
      totalClasses: statsData.length,
      scheduledClasses: statsData.filter(c => c.status === 'scheduled').length,
      ongoingClasses: statsData.filter(c => c.status === 'ongoing').length,
      completedClasses: statsData.filter(c => c.status === 'completed').length,
      cancelledClasses: statsData.filter(c => c.status === 'cancelled').length,
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error('Erro inesperado ao buscar estatísticas:', error);
    return { success: false, errors: 'Erro interno do servidor' };
  }
}

export async function getGroupClasses(groupId: string, filters: ClassFilters) {
  try {
    const supabase = await createClient();
    
    // Log para debug - remover em produção
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Buscando aulas para grupo ${groupId}`, { filters });
    }
    
    let query = supabase
      .from('classes')
      .select(`
        id,
        name,
        description,
        start_time,
        end_time,
        status,
        max_capacity,
        instructor:users (
          id,
          first_name,
          last_name,
          avatar_url
        ),
        branch:branches (
          id,
          name
        ),
        attendance (
          count
        )
      `)
      .eq('class_group_id', groupId)
      .is('deleted_at', null);

    // Aplicar filtros
    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.searchInput && filters.searchInput.trim()) {
      query = query.ilike('name', `%${filters.searchInput.trim()}%`);
    }

    if (filters.dateFrom && filters.dateTo) {
      const startOfPeriod = filters.dateFrom;
      const endOfPeriod = new Date(filters.dateTo);
      endOfPeriod.setHours(23, 59, 59, 999);
      const endOfPeriodISO = endOfPeriod.toISOString();
      
      query = query.or(
        `start_time.gte.${startOfPeriod},start_time.lte.${endOfPeriodISO},end_time.gte.${startOfPeriod},end_time.lte.${endOfPeriodISO},and(start_time.lt.${startOfPeriod},end_time.gt.${endOfPeriodISO})`
      );
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🗓️ Filtro de período: ${startOfPeriod} a ${endOfPeriodISO}`);
      }
    } else {
      if (filters.dateFrom) {
        query = query.gte('start_time', filters.dateFrom);
      }

      if (filters.dateTo) {
        const endOfDay = new Date(filters.dateTo);
        endOfDay.setHours(23, 59, 59, 999);
        const endOfDayISO = endOfDay.toISOString();
        query = query.lte('start_time', endOfDayISO);
      }
    }

    if (filters.instructorId) {
      query = query.eq('instructor_id', filters.instructorId);
    }

    // Primeira query para contar o total (sem JOINs para ter contagem correta)
    let countQuery = supabase
      .from('classes')
      .select('id', { count: 'exact', head: true })
      .eq('class_group_id', groupId)
      .is('deleted_at', null);

    // Aplicar os mesmos filtros para a contagem
    if (filters.status) {
      countQuery = countQuery.eq('status', filters.status);
    }

    if (filters.searchInput && filters.searchInput.trim()) {
      countQuery = countQuery.ilike('name', `%${filters.searchInput.trim()}%`);
    }

    if (filters.dateFrom) {
      // Filtrar aulas que começam na data especificada ou depois
      countQuery = countQuery.gte('start_time', filters.dateFrom);
    }

    if (filters.dateTo) {
      // Filtrar aulas que terminam até o final do dia especificado
      const endOfDay = new Date(filters.dateTo);
      endOfDay.setHours(23, 59, 59, 999);
      const endOfDayISO = endOfDay.toISOString();
      countQuery = countQuery.lte('end_time', endOfDayISO);
    }

    if (filters.instructorId) {
      countQuery = countQuery.eq('instructor_id', filters.instructorId);
    }

    const { count } = await countQuery;

    // Log para debug - remover em produção
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 Total de aulas encontradas: ${count}`);
    }

    // Ordenação
    query = query.order('start_time', { ascending: false });

    // Paginação
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: classes, error } = await query;
    const { count: countData, error: countError } = await countQuery;

    if (error || countError) {
      console.error('Erro ao buscar aulas da turma:', error || countError);
      return { success: false, errors: 'Erro ao buscar aulas da turma' };
    }

    const processedClasses = classes.map((classItem: any) => ({
      id: classItem.id,
      name: classItem.name,
      description: classItem.description,
      startTime: classItem.start_time,
      endTime: classItem.end_time,
      status: classItem.status,
      maxCapacity: classItem.max_capacity,
      attendanceCount: classItem.attendance?.[0]?.count || 0,
      instructor: classItem.instructor ? {
        id: classItem.instructor.id,
        name: `${classItem.instructor.first_name || ''} ${classItem.instructor.last_name || ''}`.trim(),
        avatar: classItem.instructor.avatar_url,
      } : null,
      branch: classItem.branch ? {
        id: classItem.branch.id,
        name: classItem.branch.name,
      } : null,
    }));

    const totalPages = Math.ceil((countData || 0) / limit);

    const response: ClassesResponse = {
      data: processedClasses,
      pagination: {
        page,
        limit,
        total: countData || 0,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

    return { success: true, data: response };
  } catch (error) {
    console.error('Erro inesperado ao buscar aulas:', error);
    return { success: false, errors: 'Erro interno do servidor' };
  }
}

export async function getGroupInstructors(groupId: string) {
  try {
    const supabase = await createClient();
    
    // Buscar instrutores que já deram aula nesta turma
    const { data: instructorsData, error: instructorsError } = await supabase
      .from('classes')
      .select(`
        users!instructor_id (
          id,
          first_name,
          last_name,
          avatar_url
        )
      `)
      .eq('class_group_id', groupId)
      .is('deleted_at', null);

    if (instructorsError) {
      console.error('Erro ao buscar instrutores:', instructorsError);
      return { success: false, errors: 'Erro ao carregar instrutores' };
    }

    const validInstructorsData = instructorsData.filter(item => {
      if (!item.users) {
        console.warn('Item de instrutor inválido encontrado, será ignorado:', item);
        return false;
      }
      return true;
    });

    // Remover duplicatas
    const uniqueInstructors = validInstructorsData
      .filter((item, index, self) => {
        const current = Array.isArray(item.users) ? item.users[0] : (item.users as any);
        return index === self.findIndex(i => {
          const compare = Array.isArray(i.users) ? i.users[0] : (i.users as any);
          return compare.id === current.id;
        });
      })
      .map(item => {
        const user = Array.isArray(item.users) ? item.users[0] : (item.users as any);
        return {
          id: user.id,
          name: `${user.first_name} ${user.last_name}`.trim(),
          avatar: user.avatar_url,
        };
      });

    return { success: true, data: uniqueInstructors };
  } catch (error) {
    console.error('Erro inesperado ao buscar instrutores:', error);
    return { success: false, errors: 'Erro interno do servidor' };
  }
} 